import request from './request'

/**
 * 分页查询当前登录用户的客户（从ThreadLocal获取用户信息）
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} [params.name] 客户名称(可选)
 * @param {string} [params.category] 客户分类(可选)
 * @param {string} [params.status] 合作状态(可选)
 * @returns {Promise<any>}
 */
export function fetchMyClients(params) {
	return request({
		url: '/client/my/page',
		method: 'get',
		params: {
			page: params.pageNum,
			limit: params.pageSize,
			name: params.name,
			category: params.category,
			status: params.status
		},
	})
}

/**
 * 申请新客户
 * @param {Object} data 客户信息
 * @returns {Promise<any>}
 */
export function applyClient(data) {
	return request({
		url: '/client',
		method: 'post',
		data,
	});
}

/**
 * 更新客户信息
 * @param {Object} data 客户信息
 * @returns {Promise<any>}
 */
export function updateClient(data) {
	return request({
		url: '/client',
		method: 'put',
		data,
	});
}

/**
 * 更新客户状态
 * @param {number} clientId 客户ID
 * @param {string} status 状态
 * @returns {Promise<any>}
 */
export function updateClientStatus(clientId, status) {
	return request({
		url: '/client/update-status',
		method: 'post',
		data: {
			clientId,
			status
		},
	});
}

/**
 * 根据ID获取客户详情
 * @param {number} clientId 客户ID
 * @returns {Promise<any>}
 */
export function getClientById(clientId) {
	return request({
		url: `/client/${clientId}`,
		method: 'get'
	});
}

/**
 * 获取指定部门的客户列表（分页）
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.size 每页大小
 * @param {Array<number>} params.departmentIds 部门ID列表
 * @param {string} [params.clientName] 客户名称 (可选)
 * @param {string} [params.employeeName] 负责员工姓名 (可选)
 * @param {string} [params.category] 客户分类 (可选)
 * @param {string} [params.status] 合作状态 (可选)
 */
export function getDepartmentClients(params) {
	return request({
		url: '/client/department-clients',
		method: 'post',
		data: {
			page: params.page,
			size: params.size,
			departmentIds: params.departmentIds,
			clientName: params.clientName,
			employeeName: params.employeeName,
			category: params.category,
			status: params.status,
		}
	});
}