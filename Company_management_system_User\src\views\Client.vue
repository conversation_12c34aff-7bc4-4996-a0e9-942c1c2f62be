<template>
    <div class="client-container">
        <el-card class="client-list-card">
            <!-- 头部搜索和操作栏 -->
            <div class="toolbar">
                <div class="search-box">
                    <el-input
                        v-model="searchName"
                        placeholder="搜索客户名称"
                        clearable
                        @keyup.enter="handleSearch"
                        @clear="handleSearch"
                    >
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                    <!-- 添加客户分类下拉框 -->
                    <el-select
                        v-model="searchCategory"
                        placeholder="选择客户分类"
                        clearable
                        style="width: 140px"
                    >
                        <el-option
                            v-for="item in ['海运', '空运', '散货', '快递']"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                    <!-- 添加合作状态下拉框 -->
                    <el-select
                        v-model="searchStatus"
                        placeholder="选择合作状态"
                        clearable
                        style="width: 140px"
                    >
                        <el-option
                            v-for="item in ['已合作', '报价中', '审核中', '已拒绝', '已取消']"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                    <el-button
                        type="primary"
                        @click="handleSearch"
                    >搜索</el-button>
                    <el-button @click="handleReset">
                        <el-icon><RefreshRight /></el-icon>重置
                    </el-button>
                </div>
                
                <!-- 修改申请客户按钮为添加客户按钮 -->
                <el-button
                    type="success"
                    @click="openAddDialog"
                    class="add-btn"
                >
                    <el-icon><Plus /></el-icon>
                    添加新客户
                </el-button>
            </div>

            <el-table
                v-loading="loading"
                :data="clientList"
                border
                row-key="clientId"
                :max-height="'calc(100vh - 220px)'"
                class="custom-table"
                :header-cell-style="{ background: '#f7f7f7', color: '#606266' }"
            >
                <el-table-column type="index" width="60" align="center" label="序号" class-name="index-column" />
                <el-table-column prop="name" label="客户名称" min-width="120" show-overflow-tooltip />
                <!-- 新增：联系人列 -->
                <el-table-column prop="contactPerson" label="联系人" min-width="100" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.contactPerson || '----' }}
                    </template>
                </el-table-column>
                
                <!-- 新增：负责员工列 -->
                <el-table-column prop="employeeName" label="负责员工" min-width="100" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.employeeName || '----' }}
                    </template>
                </el-table-column>

                <el-table-column prop="email" label="邮箱" min-width="150" show-overflow-tooltip />
                <el-table-column prop="phone" label="电话" min-width="130" show-overflow-tooltip />
                
                <!-- 新增：国籍/地区列 -->
                <el-table-column prop="nationality" label="国籍/地区" min-width="100" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.nationality || '----' }}
                    </template>
                </el-table-column>
                
                <!-- 新增客户分类列 -->
                <el-table-column prop="category" label="客户分类" min-width="100" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tag
                            :type="getCategoryTagType(scope.row.category)"
                            effect="plain"
                        >
                            {{ scope.row.category || '待选择' }}
                        </el-tag>
                    </template>
                </el-table-column>
                
                <!-- 新增合作状态列 -->
                <el-table-column prop="status" label="合作状态" min-width="100" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="scope.row.rejectRemark"
                            placement="top"
                            :disabled="!(scope.row.status === '已拒绝' && scope.row.rejectRemark && scope.row.rejectRemark.trim() !== '')"
                        >
                            <el-tag
                                :type="getStatusTagType(scope.row.status)"
                                effect="light"
                            >
                                {{ scope.row.status || '----' }}
                            </el-tag>
                        </el-tooltip>
                    </template>
                </el-table-column>
                
                <!-- 新增：备注列 -->
                <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.remark || '----' }}
                    </template>
                </el-table-column>
                
                <!-- 修改：操单时间列，数据源改为 operationTime -->
                <el-table-column prop="operationTime" label="操单时间" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                        {{ formatDate(scope.row.operationTime) || '----' }}
                    </template>
                </el-table-column>

                <!-- 新增：创建时间列 -->
                <el-table-column prop="createTime" label="创建时间" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                        {{ formatDate(scope.row.createTime) }}
                    </template>
                </el-table-column>
                
                <!-- 添加操作列 -->
                <el-table-column label="操作" fixed="right" min-width="220" align="center">
                    <template #default="scope">
                        <div class="operation-buttons">
                            <!-- 编辑按钮 -->
                            <el-button
                                v-if="scope.row.status !== '审核中' && scope.row.employeeName === currentUserName"
                                type="primary"
                                size="small"
                                @click="handleEdit(scope.row)"
                                title="编辑客户"
                            >
                                <el-icon><Edit /></el-icon>编辑
                            </el-button>
                            
                            <!-- 取消审批按钮 -->
                            <el-button
                                v-if="scope.row.status === '审核中' && scope.row.employeeName === currentUserName"
                                type="warning"
                                size="small"
                                @click="handleOpenCancelConfirmDialog(scope.row)"
                                title="取消审批"
                            >
                                <el-icon><Minus /></el-icon>取消审批
                            </el-button>
                            
                            <!-- 申请审批按钮 -->
                            <el-button
                                v-if="(scope.row.status === '报价中' || scope.row.status === '已拒绝' || scope.row.status === '已取消') && scope.row.employeeName === currentUserName"
                                type="success"
                                size="small"
                                @click="handleApplyApproval(scope.row)"
                                title="提交审批"
                            >
                                <el-icon><Check /></el-icon>提交新客户审批
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <div class="pagination-container">
                <el-pagination
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    v-model:currentPage="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    :page-sizes="[10, 20, 50, 100]"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>
        
        <!-- 添加客户对话框 -->
        <el-dialog
            v-model="addDialogVisible"
            title="添加新客户"
            width="500px"
            destroy-on-close
        >
            <el-form
                ref="addFormRef"
                :model="addForm"
                :rules="addFormRules"
                label-width="100px"
                label-position="right"
                status-icon
            >
                <el-form-item label="客户名称" prop="name">
                    <el-input 
                        v-model="addForm.name" 
                        placeholder="请输入客户名称"
                    />
                </el-form-item>
                <!-- 新增：联系人表单项 -->
                <el-form-item label="联系人" prop="contactPerson">
                    <el-input
                        v-model="addForm.contactPerson"
                        placeholder="请输入联系人姓名"
                    />
                </el-form-item>
                
                <el-form-item label="客户邮箱" prop="email">
                    <el-input 
                        v-model="addForm.email" 
                        placeholder="请输入客户邮箱"
                    />
                </el-form-item>
                
                <el-form-item label="联系电话" prop="phone">
                    <el-input 
                        v-model="addForm.phone" 
                        placeholder="请输入客户联系电话"
                    />
                </el-form-item>

                <el-form-item label="国籍/地区" prop="nationality">
                    <el-select 
                        v-model="addForm.nationality" 
                        placeholder="请选择国籍/地区"
                        style="width: 100%"
                        clearable
                    >
                        <el-option
                            v-for="option in nationalityOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                        />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="客户分类" prop="category">
                    <el-select 
                        v-model="addForm.category" 
                        placeholder="请选择客户分类"
                        style="width: 100%"
                    >
                        <el-option label="海运" value="海运" />
                        <el-option label="空运" value="空运" />
                        <el-option label="散货" value="散货" />
                        <el-option label="快递" value="快递" />
                    </el-select>
                </el-form-item>
                <!-- 新增：备注表单项 -->
                <el-form-item label="备注" prop="remark">
                    <el-input
                        v-model="addForm.remark"
                        type="textarea"
                        placeholder="请输入备注信息"
                        :rows="3"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelAdd">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="submitAdd"
                        :loading="addLoading"
                    >确认添加</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加编辑客户对话框 -->
        <el-dialog
            v-model="editDialogVisible"
            title="编辑客户信息"
            width="500px"
            destroy-on-close
        >
            <el-form
                ref="editFormRef"
                :model="editForm"
                :rules="addFormRules"
                label-width="100px"
                label-position="right"
                status-icon
            >
                <el-form-item label="客户名称" prop="name">
                    <el-input 
                        v-model="editForm.name" 
                        placeholder="请输入客户名称"
                        disabled
                    />
                    <div class="form-tip">客户名称不可修改</div>
                </el-form-item>
                <!-- 新增：联系人表单项 -->
                <el-form-item label="联系人" prop="contactPerson">
                    <el-input
                        v-model="editForm.contactPerson"
                        placeholder="请输入联系人姓名"
                    />
                </el-form-item>
                
                <el-form-item label="客户邮箱" prop="email">
                    <el-input 
                        v-model="editForm.email" 
                        placeholder="请输入客户邮箱"
                    />
                </el-form-item>
                
                <el-form-item label="联系电话" prop="phone">
                    <el-input 
                        v-model="editForm.phone" 
                        placeholder="请输入客户联系电话"
                    />
                </el-form-item>

                <el-form-item label="国籍/地区" prop="nationality">
                    <el-select 
                        v-model="editForm.nationality" 
                        placeholder="请选择国籍/地区"
                        style="width: 100%"
                        clearable
                    >
                        <el-option
                            v-for="option in nationalityOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                        />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="客户分类" prop="category">
                    <el-select 
                        v-model="editForm.category" 
                        placeholder="请选择客户分类"
                        style="width: 100%"
                    >
                        <el-option label="海运" value="海运" />
                        <el-option label="空运" value="空运" />
                        <el-option label="散货" value="散货" />
                        <el-option label="快递" value="快递" />
                    </el-select>
                </el-form-item>
                
                <!-- 新增：备注表单项 -->
                <el-form-item label="备注" prop="remark">
                    <el-input
                        v-model="editForm.remark"
                        type="textarea"
                        placeholder="请输入备注信息"
                        :rows="3"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelEdit">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="submitEdit"
                        :loading="editLoading"
                    >保存修改</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加申请审批确认对话框 -->
        <el-dialog
            v-model="approvalDialogVisible"
            title="申请审批确认"
            width="400px"
        >
            <div class="approval-confirm-content">
                <p>确定要将客户 <strong>{{ currentClient?.name }}</strong> 提交审批吗？</p>
                <p>提交后客户状态将变更为 <el-tag type="info">审核中</el-tag>，等待管理员审核。</p>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="approvalDialogVisible = false">取消</el-button>
                    <el-button 
                        type="success" 
                        @click="confirmApplyApproval"
                        :loading="approvalLoading"
                    >确认提交</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 新增：取消审批确认对话框 -->
        <el-dialog
            v-model="cancelApprovalConfirmDialogVisible"
            title="取消审批确认"
            width="400px"
            append-to-body
            destroy-on-close
        >
            <div class="approval-confirm-content">
                <p>确定要取消客户 <strong>{{ clientToCancel?.name }}</strong> 的审批申请吗？</p>
                <p>取消后客户状态将变更为 <el-tag :type="getStatusTagType('已取消')" effect="light">已取消</el-tag>。</p>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelApprovalConfirmDialogVisible = false">返回</el-button>
                    <el-button 
                        type="warning" 
                        @click="confirmCancelApprovalAction"
                        :loading="cancelApprovalLoading"
                    >确认取消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { fetchMyClients, applyClient, updateClient, updateClientStatus, getClientById} from '@/api/client';
import { Search, RefreshRight, Plus, Edit, Check, Minus } from '@element-plus/icons-vue';
import { useAuthStore } from '@/stores/token';

// 数据加载状态
const loading = ref(false);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索和筛选
const searchName = ref('');
const searchCategory = ref(''); // 用于存储选中的客户分类
const searchStatus = ref(''); // 用于存储选中的合作状态

// 客户列表
const clientList = ref([]);

// 添加客户对话框
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const addLoading = ref(false);

// 新增：国籍/地区选项
const nationalityOptions = ref([
    { value: '国内-同行', label: '国内-同行' },
    { value: '国外-同行', label: '国外-同行' },
    { value: '国内-直客', label: '国内-直客' },
    { value: '国外-直客', label: '国外-直客' },
]);

// 添加客户表单数据
const addForm = reactive({
    name: '',
    email: '',
    phone: '',
    contactPerson: '', // 新增
    nationality: '', // 保持为空，以便placeholder生效
    category: '海运',
    status: '报价中',
    remark: '' // 新增
});

// 添加客户表单验证规则
const addFormRules = {
    name: [
        { required: true, message: '请输入客户名称', trigger: 'blur' },
        { min: 2, max: 50, message: '客户名称长度必须在2-50个字符之间', trigger: 'blur' }
    ],
    email: [
        { required: true, message: '请输入客户邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
        { max: 100, message: '邮箱长度不能超过100个字符', trigger: 'blur' }
    ],

    category: [
        { required: true, message: '请选择客户分类', trigger: 'change' }
    ]
};

// 编辑客户对话框
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const editLoading = ref(false);
const editForm = reactive({
    clientId: null,
    name: '',
    email: '',
    phone: '',
    contactPerson: '', // 新增
    nationality: '', // 保持为空，或由handleEdit填充
    category: '',
    status: '',
    remark: '' // 新增
});

// 申请审批对话框
const approvalDialogVisible = ref(false);
const approvalLoading = ref(false);
const currentClient = ref(null);

// 新增：取消审批相关
const cancelApprovalConfirmDialogVisible = ref(false);
const cancelApprovalLoading = ref(false);
const clientToCancel = ref(null);

// 使用 auth store 获取当前登录用户名称
const authStore = useAuthStore();
const currentUserName = computed(() => {
    // !!! 重要：请根据您 user store 中实际存储用户名的字段进行调整 !!!
    // 例如，如果字段是 employeeName: return authStore.user?.employeeName || '';
    // 假设字段是 name:
    return authStore.user?.name || ''; 
});

// 生命周期钩子
onMounted(() => {
    // 不再需要在这里手动设置 currentUserName
    fetchData();
});

// 获取客户列表数据
const fetchData = async () => {
    loading.value = true;
    try {
        const res = await fetchMyClients({
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            name: searchName.value || undefined,
            category: searchCategory.value || undefined,
            status: searchStatus.value || undefined
        });
        
        if (res.code === 200) {
            clientList.value = res.data.list || [];
            total.value = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取客户列表失败');
        }
    } catch (error) {
        console.error('获取客户列表失败', error);
        ElMessage.error('网络错误，请稍后重试');
    } finally {
        loading.value = false;
    }
};

// 根据分类设置不同的Tag样式
const getCategoryTagType = (category) => {
    switch (category) {
        case '海运':
            return 'primary';
        case '空运':
            return 'success';
        case '散货':
            return 'warning';
        case '快递':
            return 'info';
        default:
            return '';
    }
};

// 根据状态设置不同的Tag样式
const getStatusTagType = (status) => {
    switch (status) {
        case '已合作':
            return 'success';
        case '报价中':
            return 'warning';
        case '审核中':
            return 'info';
        case '已拒绝':
            return 'danger';
        case '已取消':
            return 'default';
        default:
            return '';
    }
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// 搜索处理
const handleSearch = () => {
    currentPage.value = 1;
    fetchData();
};

// 重置筛选条件
const handleReset = () => {
    searchName.value = '';
    searchCategory.value = '';
    searchStatus.value = '';
    currentPage.value = 1;
    fetchData();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
    pageSize.value = val;
    fetchData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchData();
};

// 打开添加客户对话框
const openAddDialog = () => {
    // 重置表单数据
    if (addFormRef.value) {
        addFormRef.value.resetFields();
    } else {
        // 如果表单引用还不存在，手动重置表单数据
        addForm.name = '';
        addForm.email = '';
        addForm.phone = '';
        addForm.contactPerson = ''; // 新增
        addForm.nationality = '';
        addForm.category = '海运';
        addForm.remark = ''; // 新增
    }
    
    // 显示对话框
    addDialogVisible.value = true;
};

// 取消添加
const cancelAdd = () => {
    addDialogVisible.value = false;
};

// 提交添加
const submitAdd = async () => {
    // 表单验证
    if (!addFormRef.value) return;
    
    await addFormRef.value.validate(async (valid) => {
        if (!valid) {
            ElMessage.warning('请正确填写表单信息');
            return;
        }
        
        // 设置加载状态
        addLoading.value = true;
        
        try {
            // 确保状态为报价中
            addForm.status = '报价中';
            
            // 发送添加客户请求
            const res = await applyClient({...addForm});
            
            if (res.code === 200) {
                ElMessage.success('客户添加成功');
                
                // 关闭对话框
                addDialogVisible.value = false;
                
                // 刷新客户列表
                fetchData();
            } else if (res.code === 400) {
                // 处理验证错误 - 显示黄色警告
                ElMessage({
                    type: 'warning',
                    message: res.message,
                    duration: 5000
                });
                
                // 如果是客户名称已存在的错误，将焦点设置到名称输入框
                if (res.message && res.message.includes('客户名称') && res.message.includes('已存在')) {
                    // 将焦点设置到名称输入框
                    if (addFormRef.value) {
                        // 等待DOM更新
                        setTimeout(() => {
                            // 查找名称输入框并设置焦点
                            const nameInput = addFormRef.value.$el.querySelector('input[placeholder="请输入客户名称"]');
                            if (nameInput) {
                                nameInput.focus();
                                nameInput.select(); // 选中文本便于用户直接修改
                            }
                        }, 100);
                    }
                }
            } else {
                // 处理其他错误
                ElMessage.error(res.message || '客户添加失败');
            }
        } catch (error) {
            console.error('添加客户失败', error);
            ElMessage.error('网络错误，请稍后重试');
        } finally {
            // 取消加载状态
            addLoading.value = false;
        }
    });
};

// 编辑客户
const handleEdit = async (row) => {
    try {
        // 获取最新的客户信息
        const res = await getClientById(row.clientId);
        if (res.code === 200) {
            // 填充表单
            Object.assign(editForm, res.data);
            // 确保新字段被正确赋值，如果后端字段名不一致或可能不存在
            editForm.contactPerson = res.data.contactPerson || '';
            editForm.remark = res.data.remark || '';
            // editForm.rejectRemark = res.data.rejectRemark || ''; // 用户端不编辑此项
            // 打开编辑对话框
            editDialogVisible.value = true;
        } else {
            ElMessage.error(res.message || '获取客户信息失败');
        }
    } catch (error) {
        console.error('获取客户信息失败', error);
        ElMessage.error('网络错误，请稍后重试');
    }
};

// 取消编辑
const cancelEdit = () => {
    editDialogVisible.value = false;
};

// 提交编辑
const submitEdit = async () => {
    // 表单验证
    if (!editFormRef.value) return;
    
    await editFormRef.value.validate(async (valid) => {
        if (!valid) {
            ElMessage.warning('请正确填写表单信息');
            return;
        }
        
        // 设置加载状态
        editLoading.value = true;
        
        try {
            // 发送更新客户请求
            const res = await updateClient({...editForm});
            
            if (res.code === 200) {
                ElMessage.success('客户信息更新成功');
                
                // 关闭对话框
                editDialogVisible.value = false;
                
                // 刷新客户列表
                fetchData();
            } else {
                ElMessage.error(res.message || '客户信息更新失败');
            }
        } catch (error) {
            console.error('更新客户信息失败', error);
            ElMessage.error('网络错误，请稍后重试');
        } finally {
            // 取消加载状态
            editLoading.value = false;
        }
    });
};

// 申请审批
const handleApplyApproval = (row) => {
    // 保存当前客户信息
    currentClient.value = row;
    // 打开确认对话框
    approvalDialogVisible.value = true;
};

// 新增：打开取消审批确认对话框
const handleOpenCancelConfirmDialog = (row) => {
    clientToCancel.value = row;
    cancelApprovalConfirmDialogVisible.value = true;
};

// 新增：确认取消审批动作
const confirmCancelApprovalAction = async () => {
    if (!clientToCancel.value) return;
    
    cancelApprovalLoading.value = true;
    try {
        const res = await updateClientStatus(clientToCancel.value.clientId, '已取消');
        if (res.code === 200) {
            ElMessage.success('客户审批已取消');
            cancelApprovalConfirmDialogVisible.value = false;
            fetchData(); // 刷新客户列表
        } else {
            ElMessage.error(res.message || '取消审批失败');
        }
    } catch (error) {
        console.error('取消审批失败:', error);
        ElMessage.error('网络错误，请稍后重试');
    } finally {
        cancelApprovalLoading.value = false;
    }
};

// 确认申请审批
const confirmApplyApproval = async () => {
    if (!currentClient.value) return;
    
    approvalLoading.value = true;
    
    try {
        // 更新客户状态为"审核中"
        const res = await updateClientStatus(currentClient.value.clientId, '审核中');
        
        if (res.code === 200) {
            ElMessage.success('客户已成功提交审批');
            
            // 关闭对话框
            approvalDialogVisible.value = false;
            
            // 刷新客户列表
            fetchData();
        } else {
            ElMessage.error(res.message || '提交审批失败');
        }
    } catch (error) {
        console.error('提交审批失败', error);
        ElMessage.error('网络错误，请稍后重试');
    } finally {
        approvalLoading.value = false;
    }
};
</script>

<style scoped>
.client-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-box .el-input {
    width: 220px;
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

/* 添加客户按钮样式 */
.add-btn {
    font-weight: bold;
    padding: 10px 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.add-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 操作列按钮容器样式 */
.operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    gap: 10px;
}

/* 操作列按钮样式 */
:deep(.el-table .operation-buttons .el-button) {
    border-radius: 4px;
    padding: 6px 12px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

:deep(.el-table .operation-buttons .el-button .el-icon) {
    margin-right: 4px;
    vertical-align: middle;
}

/* 对话框表单样式 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 12px;
}

:deep(.el-form-item__label) {
    font-weight: 500;
}

.form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 4px;
    padding-left: 2px;
}

:deep(.el-input.is-disabled .el-input__inner) {
    color: #666;
    cursor: not-allowed;
    background-color: #f5f7fa;
}

:deep(.el-dialog__header) {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 20px;
    margin-bottom: 10px;
}

:deep(.el-dialog__title) {
    font-weight: bold;
    font-size: 18px;
    color: #409EFF;
}

/* 审批确认对话框样式 */
.approval-confirm-content {
    padding: 20px;
    text-align: center;
    font-size: 16px;
}

.approval-confirm-content p {
    margin: 10px 0;
}

.approval-confirm-content strong {
    color: #409EFF;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .search-box {
        width: 100%;
    }
    
    .search-box .el-input {
        width: 100%;
    }
    
    .add-btn {
        margin-top: 10px;
        width: 100%;
    }
    
    .pagination-container {
        position: static;
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
    
    .operation-buttons {
        flex-direction: column;
        gap: 5px;
    }
}
</style> 