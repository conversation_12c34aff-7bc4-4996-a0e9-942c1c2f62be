package org.example.company_management.service.impl;

import org.example.company_management.entity.*;
import org.example.company_management.mapper.*;
import org.example.company_management.service.NotificationService;
import org.example.company_management.service.NotificationRuleService;
import org.example.company_management.utils.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.HashMap;


@Service
public class NotificationServiceImpl implements NotificationService {

    private static final Logger log = LoggerFactory.getLogger(NotificationServiceImpl.class);
    private static final String BUSINESS_STAFF_POSITION_NAME = "业务员";
    private static final String CLIENT_STATUS_COOPERATED = "已合作";
    private static final String RULE_PERFORMANCE_VS_SALARY = "PERFORMANCE_VS_SALARY";
    private static final String RULE_NEW_CLIENTS_LAST_MONTH = "NEW_CLIENTS_LAST_MONTH";

    private final EmployeeNotificationMapper employeeNotificationMapper;
    private final NotificationRuleService notificationRuleService;
    private final EmployeeMapper employeeMapper;
    private final PerformanceMapper performanceMapper;
    private final SalaryMapper salaryMapper;
    private final ClientMapper clientMapper;
    private final EmployeeOtherExpenseMapper employeeOtherExpenseMapper;

    @Autowired
    public NotificationServiceImpl(EmployeeNotificationMapper employeeNotificationMapper,
                                 NotificationRuleService notificationRuleService, 
                                 EmployeeMapper employeeMapper,
                                 PerformanceMapper performanceMapper,
                                 SalaryMapper salaryMapper,
                                 ClientMapper clientMapper,
                                 EmployeeOtherExpenseMapper employeeOtherExpenseMapper) {
        this.employeeNotificationMapper = employeeNotificationMapper;
        this.notificationRuleService = notificationRuleService;
        this.employeeMapper = employeeMapper;
        this.performanceMapper = performanceMapper;
        this.salaryMapper = salaryMapper;
        this.clientMapper = clientMapper;
        this.employeeOtherExpenseMapper = employeeOtherExpenseMapper;
    }

    @Override
    @Transactional
    public void generateNotificationsForEmployee(Employee employee) {
        if (employee == null || employee.getEmployeeId() == null) {
            log.warn("尝试为null或没有ID的员工生成通知.");
            return;
        }

        // 获取员工职位信息
        Employee empWithPosition = employeeMapper.findByIdWithPositionName(employee.getEmployeeId());
        
        List<NotificationRule> positionSpecificRules = new ArrayList<>();
        if (empWithPosition != null && StringUtils.isNotBlank(empWithPosition.getPositionName())) {
            positionSpecificRules = notificationRuleService.getActiveRulesByPositionName(empWithPosition.getPositionName());
        } else {
            log.warn("员工 {} (ID: {}) 没有有效的职位信息，将仅处理通用通知规则。", employee.getName(), employee.getEmployeeId());
            // empWithPosition 可能为 null 或者其 positionName 为空
        }

        List<NotificationRule> generalRules = notificationRuleService.getActiveRulesForAllPositions();

        List<NotificationRule> allApplicableRules = new ArrayList<>();
        if (positionSpecificRules != null) { // positionSpecificRules is initialized, so it's not null
            allApplicableRules.addAll(positionSpecificRules);
        }
        if (generalRules != null) {
            allApplicableRules.addAll(generalRules);
        }
        // Optional: Deduplicate and sort if necessary, e.g., by ruleId then priority.
        // For simplicity, assuming rule IDs from position-specific and general rules won't clash
        // or that processing duplicates 괜찮아.

        if (allApplicableRules.isEmpty()) {
            log.info("员工 {} (职位 {}) 没有配置活动的或通用的通知规则.", employee.getEmployeeId(), empWithPosition != null ? empWithPosition.getPositionName() : "无职位");
            return;
        }

        LocalDate today = LocalDate.now();
        YearMonth lastMonth = YearMonth.now().minusMonths(1);
        YearMonth currentMonth = YearMonth.now();
        String lastMonthStr = lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));

        for (NotificationRule rule : allApplicableRules) {
            boolean isFirstOfMonth = today.getDayOfMonth() == 1;
            boolean isMonthlyServerRefreshRule = RULE_PERFORMANCE_VS_SALARY.equals(rule.getConditionType()) ||
                                                 RULE_NEW_CLIENTS_LAST_MONTH.equals(rule.getConditionType());

            boolean skipGenerationDueToExisting = false;
            EmployeeNotification blockingNotification = employeeNotificationMapper.findExistingNotificationByRule(
                employee.getEmployeeId(), rule.getRuleId(), today
            );

            if (blockingNotification != null) {
                if (isFirstOfMonth && isMonthlyServerRefreshRule && "ACTIVE".equals(blockingNotification.getStatus())) {
                    YearMonth blockerGeneratedMonth = YearMonth.from(blockingNotification.getGeneratedAt());
                    // currentMonth is already defined above, let's rename for clarity in this scope
                    YearMonth currentEvaluationMonth = currentMonth; 
                    if (blockerGeneratedMonth.isBefore(currentEvaluationMonth)) {
                        // This is an old ACTIVE notification from a previous cycle. Don't let it block.
                        skipGenerationDueToExisting = false;
                        log.info("员工 {} 规则 '{}': 发现老的ACTIVE通知 (ID {}, 生成于 {}), 当前为 {} 月1号评估 {} 数据, 将为其生成新通知.",
                                 employee.getEmployeeId(), rule.getRuleName(), blockingNotification.getNotificationId(), 
                                 blockerGeneratedMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")), 
                                 currentEvaluationMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")),
                                 lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"))
                                 );
                    } else {
                        // ACTIVE notification is from current cycle (e.g. generated today, or re-prompted this month). Let it block.
                        skipGenerationDueToExisting = true;
                    }
                } else {
                    // Either not (1st of month AND monthly server refresh rule AND ACTIVE status), 
                    // or it's a DISMISSED notification with future next_prompt_date.
                    // In these cases, the original logic applies: if blockingNotification exists, it blocks.
                    skipGenerationDueToExisting = true;
                }
            }

            if (skipGenerationDueToExisting) {
                log.debug("员工 {} 规则 '{}' ({}) 已存在有效的通知 {} (状态: {}, 下次提醒: {}), 跳过生成.", 
                    employee.getEmployeeId(), rule.getRuleName(), rule.getRuleId(), 
                    blockingNotification.getNotificationId(), blockingNotification.getStatus(), blockingNotification.getNextPromptDate());
                continue; // Skip to next rule
            }
            
            // If we reach here, either no blocking notification exists, or it was an old ACTIVE one that we decided to override.

            boolean conditionMet = false;
            Map<String, String> messageParams = new HashMap<>();
            messageParams.put("{userName}", employee.getName());
            String titleCn = StringUtils.isNotBlank(rule.getDescription()) ? rule.getDescription() : rule.getRuleName();

            switch (rule.getConditionType()) {
                case RULE_PERFORMANCE_VS_SALARY: // 上月业绩不达标
                    Optional<Performance> performanceOpt = performanceMapper.findByEmployeeIdAndDate(employee.getEmployeeId(), lastMonthStr);
                    Optional<Salary> salaryOpt = salaryMapper.findByEmployeeIdAndDate(employee.getEmployeeId(), lastMonthStr);
                    BigDecimal otherExpenses = employeeOtherExpenseMapper.sumAmountByEmployeeIdAndYearMonth(employee.getEmployeeId(), lastMonthStr);
                    otherExpenses = otherExpenses == null ? BigDecimal.ZERO : otherExpenses;

                    if (salaryOpt.isPresent()) {
                        Salary salary = salaryOpt.get();
                        // 计算应得工资: 实得工资 - 请假扣款 - 迟到缺卡扣款 + 上月员工其他费用
                        BigDecimal calculatedSalary = salary.getSumSalary()
                                .subtract(salary.getLeaveDeduction() != null ? salary.getLeaveDeduction() : BigDecimal.ZERO)
                                .subtract(salary.getLateDeduction() != null ? salary.getLateDeduction() : BigDecimal.ZERO)
                                .add(otherExpenses);
                        
                        BigDecimal actualPerformance = performanceOpt.map(Performance::getActualPerformance).orElse(BigDecimal.ZERO);
                        messageParams.put("{actualPerformance}", actualPerformance.toString());
                        messageParams.put("{calculatedSalary}", calculatedSalary.toString());
                        titleCn = "上月业绩提醒";

                        if (actualPerformance.compareTo(calculatedSalary) < 0) {
                            conditionMet = true;
                        }
                    } else {
                         log.info("员工 {} 上月 ({}) 无工资记录, 无法判断业绩是否达标.", employee.getEmployeeId(), lastMonthStr);
                    }
                    break;

                case RULE_NEW_CLIENTS_LAST_MONTH: // 上月无新客户
                    LocalDateTime lastMonthStart = lastMonth.atDay(1).atStartOfDay();
                    LocalDateTime currentMonthStartEval = currentMonth.atDay(1).atStartOfDay(); // Renamed to avoid conflict
                    long newClientsLastMonth = clientMapper.countByEmployeeIdAndStatusAndCreateTimeBetween(
                            employee.getEmployeeId(), CLIENT_STATUS_COOPERATED,
                            lastMonthStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                            currentMonthStartEval.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                    );
                    titleCn = "上月新客户提醒";
                    if (newClientsLastMonth == 0) {
                        conditionMet = true;
                    }
                    break;

                case "NEW_CLIENTS_THIS_MONTH_AFTER_15TH": // 本月过半无新客户
                     if (today.getDayOfMonth() > 15) {
                        LocalDateTime monthStart = currentMonth.atDay(1).atStartOfDay();
                        LocalDateTime tomorrowStart = today.plusDays(1).atStartOfDay(); // count up to end of today
                        long newClientsThisMonth = clientMapper.countByEmployeeIdAndStatusAndCreateTimeBetween(
                                employee.getEmployeeId(), CLIENT_STATUS_COOPERATED,
                                monthStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                                tomorrowStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                        );
                        titleCn = "本月客户开发提醒";
                        if (newClientsThisMonth == 0) {
                            conditionMet = true;
                        }
                    } else {
                        log.debug("规则 'NEW_CLIENTS_THIS_MONTH_AFTER_15TH' 未到15号之后，跳过员工 {}.", employee.getEmployeeId());
                    }
                    break;
                case "ALWAYS_TRIGGER":
                    conditionMet = true;
                    // titleCn can be further customized if needed, e.g. from rule description or name
                    break;
                default:
                    log.warn("未知的通知规则条件类型: {}", rule.getConditionType());
            }

            if (conditionMet) {
                String messageContent = rule.getMessageTemplateCn();
                for (Map.Entry<String, String> entry : messageParams.entrySet()) {
                    messageContent = messageContent.replace(entry.getKey(), entry.getValue());
                }

                EmployeeNotification newNotification = EmployeeNotification.builder()
                        .employeeId(employee.getEmployeeId())
                        .ruleId(rule.getRuleId())
                        .notificationType(rule.getConditionType())
                        .titleCn(titleCn)
                        .messageCn(messageContent)
                        .generatedAt(LocalDateTime.now())
                        .showAgainAfterDays(rule.getDefaultShowAgainAfterDays())
                        .isReadInBell(false)
                        .status("ACTIVE") // 新通知为ACTIVE
                        .build();
                employeeNotificationMapper.insertNotification(newNotification);
                log.info("为员工 {} 生成新通知: Rule '{}', ID {}. 内容: {}", 
                    employee.getEmployeeId(), rule.getRuleName(), newNotification.getNotificationId(), newNotification.getMessageCn());
            }
        }
    }

    @Override
    public List<EmployeeNotification> getBellNotificationsForEmployee(Integer employeeId) {
        if (employeeId == null) return Collections.emptyList();
        return employeeNotificationMapper.findNotificationsForBellByEmployeeId(employeeId);
    }

    @Override
    @Transactional
    public Result<?> dismissNotification(Long notificationId, Integer employeeId) {
        EmployeeNotification notification = employeeNotificationMapper.findById(notificationId);
        if (notification == null) {
            return Result.error("通知不存在!");
        }
        if (!notification.getEmployeeId().equals(employeeId)) {
            return Result.error("无权操作此通知!");
        }

        LocalDate nextPromptDate = null;
        Integer showAgainDays = notification.getShowAgainAfterDays();
        if (showAgainDays != null && showAgainDays > 0) {
            nextPromptDate = LocalDate.now().plusDays(showAgainDays);
        }
        
        int updatedRows = employeeNotificationMapper.dismissNotificationById(
            notificationId, 
            LocalDateTime.now(), 
            nextPromptDate, 
            "DISMISSED"
        );
        if (updatedRows > 0) {
            return Result.success("通知已处理");
        } else {
            return Result.error("处理通知失败");
        }
    }

    @Override
    @Transactional
    public Result<?> markAsReadInBell(Long notificationId, Integer employeeId) {
        EmployeeNotification notification = employeeNotificationMapper.findById(notificationId);
        if (notification == null) {
            return Result.error("通知不存在!");
        }
        if (!notification.getEmployeeId().equals(employeeId)) {
            return Result.error("无权操作此通知!");
        }
        // 只有ACTIVE且未读的才需要标记，DISMISSED的通常在dismiss时就标记为已读了
        if ("ACTIVE".equals(notification.getStatus()) && !Boolean.TRUE.equals(notification.getIsReadInBell())){
            int updatedRows = employeeNotificationMapper.markAsReadInBellById(notificationId, true);
            if (updatedRows > 0) {
                 return Result.success("通知已标记为已读");
            } else {
                return Result.error("标记已读失败");
            }
        }
        return Result.success("通知状态无需变更"); //已经是已读或者不是ACTIVE状态
    }

    @Override
    @Transactional
    public Result<?> dismissMultipleNotifications(List<Long> notificationIds, Integer employeeId) {
        if (notificationIds == null || notificationIds.isEmpty()) {
            return Result.error("请选择要处理的通知");
        }
        // 简单的权限检查：可以检查第一个通知的所有权，或者在mapper中进行更复杂的处理
        // 为简化，这里假设如果能拿到这些ID，就有权限，或者在controller层做了用户ID的校验
        // 更严谨的做法是逐条验证或在SQL层面加入employeeId判断
        
        // 计算一个统一的下次提醒日期，这里简单处理，所有批量dismiss的通知可以基于各自规则重新计算，
        // 或者使用一个统一的默认值。由于规则中下次提醒天数可能不同，此处设置为null，由单个dismiss逻辑决定。
        // 如果需要每个通知有自己的next_prompt_date，则需要批量查询规则，然后批量更新。
        // 当前的mapper.dismissMultipleNotificationsByIds 使用一个统一的nextPromptDate。
        // 为了和单个dismiss行为一致，并且利用规则中的天数，我们最好逐条处理，或者修改批量接口逻辑。
        // 这里，为了使用已有的批量SQL，我们先用一个固定的nextPromptDate（或者null）。
        // 但更符合逻辑的是单个处理，或后端支持传一个map<id, nextPromptDate>.
        // 考虑到用户点击"全部处理"的语义，可能是让它们在各自的间隔后再次出现。

        // 方案1: 逐条调用单个dismiss (更符合逻辑，但可能多次DB交互)
        int successCount = 0;
        for (Long id : notificationIds) {
            try {
                Result<?> singleDismissResult = dismissNotification(id, employeeId); // 这会使用各自的规则计算next_prompt_date
                if (singleDismissResult.getCode() == 200) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("处理通知ID {} 时发生错误: {}", id, e.getMessage());
            }
        }
        if (successCount == notificationIds.size()){
            return Result.success("所有选中通知已处理");
        } else if (successCount > 0){
            return Result.success("部分通知已处理 (" + successCount + "/" + notificationIds.size() + ")");
        } else {
            return Result.error("批量处理通知失败");
        }

        // 方案2: 使用批量SQL (如果所有通知的 next_prompt_date 都一样，或者都为null)
        /* 
        LocalDate commonNextPromptDate = LocalDate.now().plusDays(7); // 示例：统一7天后
        int updatedRows = employeeNotificationMapper.dismissMultipleNotificationsByIds(
            notificationIds, 
            LocalDateTime.now(), 
            commonNextPromptDate, // 或者 null
            "DISMISSED"
        );
        if (updatedRows > 0) {
            return Result.success("选中的 " + updatedRows + " 条通知已处理");
        } else {
            return Result.error("批量处理通知失败或没有通知被更新");
        }
        */
    }

    @Override
    public List<EmployeeNotification> getPopupNotificationsForEmployee(Integer employeeId) {
        // 当前设计下，登录弹窗是通过 getBellNotifications然后前端判断 shouldOpenCenterOnLoginIfUnread
        // 如果确实还需要一个独立的popup接口，它可以查找所有 ACTIVE 且 next_prompt_date已到期或null的通知
        // return employeeNotificationMapper.findActiveAndDueByEmployeeId(employeeId, LocalDate.now());
        log.info("getPopupNotificationsForEmployee 被调用，但当前主要通过铃铛通知逻辑处理弹窗。");
        return Collections.emptyList(); // 或者返回与 getBellNotificationsForEmployee 相同的结果让前端决定
    }

    @Async
    @Override
    public void generateNotificationsForEmployeeAsync(Employee employee) {
        log.info("发起异步通知生成任务，员工ID: {}", employee != null ? employee.getEmployeeId() : "NULL_EMPLOYEE");
        if (employee == null) {
            log.warn("generateNotificationsForEmployeeAsync 接收到 null employee 对象，任务终止。");
            return;
        }
        try {
            this.generateNotificationsForEmployee(employee); // Call the original synchronous method
            log.info("异步通知生成任务完成，员工ID: {}", employee.getEmployeeId());
        } catch (Exception e) {
            log.error("异步生成员工 {} ({}) 的通知时发生错误: {}", 
                employee.getName(), employee.getEmployeeId(), e.getMessage(), e);
            // Consider more sophisticated error handling here if needed,
            // e.g., sending to a dead-letter queue or retrying.
        }
    }
} 