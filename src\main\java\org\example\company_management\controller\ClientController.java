package org.example.company_management.controller;

import org.example.company_management.entity.Client;
import org.example.company_management.service.ClientService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户管理控制器
 */
@RestController
@RequestMapping("/client")
public class ClientController {

    @Autowired
    private ClientService clientService;

    /**
     * 根据ID获取客户信息
     *
     * @param clientId 客户ID
     * @return 客户信息
     */
    @GetMapping("/{clientId}")
    public Result<Client> getClientById(@PathVariable Integer clientId) {
        Client client = clientService.getClientById(clientId);
        if (client != null) {
            return Result.success(client);
        } else {
            return Result.notFound("客户不存在");
        }
    }

    /**
     * 获取所有客户信息
     *
     * @return 客户列表
     */
    @GetMapping("/all")
    public Result<List<Client>> getAllClients() {
        List<Client> clients = clientService.getAllClients();
        return Result.success(clients);
    }

    /**
     * 分页获取客户列表
     *
     * @param page       页码
     * @param limit      每页条数
     * @param name       客户名称（可选）
     * @param departmentId 部门ID（可选）
     * @param employeeName 员工名称（可选）
     * @param category   客户分类（可选）
     * @param status     合作状态（可选）
     * @return 分页数据
     */
    @GetMapping("/page")
    public Result<PageResult<Client>> getClientsByPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status) {

        Map<String, Object> params = new HashMap<>();
        params.put("start", (page - 1) * limit);
        params.put("limit", limit);

        if (name != null && !name.trim().isEmpty()) {
            params.put("name", name);
        }

        if (departmentId != null) {
            params.put("departmentId", departmentId);
        }

        if (employeeName != null && !employeeName.trim().isEmpty()) {
            params.put("employeeName", employeeName);
        }

        if (category != null && !category.trim().isEmpty()) {
            params.put("category", category);
        }

        if (status != null && !status.trim().isEmpty()) {
            params.put("status", status);
        }

        List<Client> clients = clientService.getClientsByPage(params);
        int total = clientService.getClientCount(params);

        PageResult<Client> pageResult = new PageResult<>(page, limit, total, clients);
        return Result.success(pageResult);
    }

    /**
     * 添加客户
     *
     * @param client 客户信息
     * @return 操作结果
     */
    @PostMapping
    public Result<Integer> addClient(@RequestBody Client client) {
        return clientService.saveClient(client);
    }

    /**
     * 更新客户信息
     *
     * @param client 客户信息
     * @return 操作结果
     */
    @PutMapping
    public Result<Void> updateClient(@RequestBody Client client) {
        if (client.getClientId() == null) {
            return Result.validateFailed("客户ID不能为空");
        }

        int result = clientService.updateClient(client);
        if (result > 0) {
            return Result.success("更新客户成功", null);
        } else {
            return Result.error("更新客户失败，客户可能不存在");
        }
    }

    /**
     * 删除客户
     *
     * @param clientId 客户ID
     * @return 操作结果
     */
    @DeleteMapping("/{clientId}")
    public Result<Void> deleteClient(@PathVariable Integer clientId) {
        int result = clientService.deleteClient(clientId);
        if (result > 0) {
            return Result.success("删除客户成功", null);
        } else {
            return Result.error("删除客户失败，客户可能不存在");
        }
    }

    /**
     * 根据员工ID获取其负责的客户列表
     *
     * @param employeeId 员工ID
     * @return 客户列表
     */
    @GetMapping("/employee/{employeeId}")
    public Result<List<Client>> getClientsByEmployee(@PathVariable Integer employeeId) {
        List<Client> clients = clientService.getClientsByEmployee(employeeId);
        return Result.success(clients);
    }

    /**
     * 根据名称查询客户
     *
     * @param name 客户名称
     * @return 客户列表
     */
    @GetMapping("/search/name")
    public Result<List<Client>> getClientsByName(@RequestParam String name) {
        List<Client> clients = clientService.getClientsByName(name);
        return Result.success(clients);
    }

    /**
     * 根据邮箱查询客户
     *
     * @param email 客户邮箱
     * @return 客户信息
     */
    @GetMapping("/search/email")
    public Result<Client> getClientByEmail(@RequestParam String email) {
        Client client = clientService.getClientByEmail(email);
        if (client != null) {
            return Result.success(client);
        } else {
            return Result.notFound("未找到对应邮箱的客户");
        }
    }

    /**
     * 根据电话查询客户
     *
     * @param phone 客户电话
     * @return 客户信息
     */
    @GetMapping("/search/phone")
    public Result<Client> getClientByPhone(@RequestParam String phone) {
        Client client = clientService.getClientByPhone(phone);
        if (client != null) {
            return Result.success(client);
        } else {
            return Result.notFound("未找到对应电话的客户");
        }
    }

    /**
     * 分页获取当前登录用户的客户列表
     *
     * @param page         页码
     * @param limit        每页条数
     * @param name         客户名称（可选）
     * @param category     客户分类（可选）
     * @param status       审批状态（可选）
     * @param clientStatus 客户状态（可选）
     * @return 分页数据
     */
    @GetMapping("/my/page")
    public Result<PageResult<Client>> getMyClientsByPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String clientStatus) {

        Map<String, Object> params = new HashMap<>();
        params.put("start", (page - 1) * limit);
        params.put("limit", limit);

        if (name != null && !name.trim().isEmpty()) {
            params.put("name", "%" + name + "%");
        }

        if (category != null && !category.trim().isEmpty()) {
            params.put("category", category);
        }

        if (status != null && !status.trim().isEmpty()) {
            params.put("status", status);
        }

        if (clientStatus != null && !clientStatus.trim().isEmpty()) {
            params.put("clientStatus", clientStatus);
        }

        try {
            List<Client> clients = clientService.getMyClientsByPage(params);
            int total = clientService.getMyClientCount(params);

            PageResult<Client> pageResult = new PageResult<>(page, limit, total, clients);
            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取待审批的客户列表（状态为"审核中"的客户）
     *
     * @return 待审批客户列表
     */
    @GetMapping("/pending")
    public Result<List<Client>> getPendingClients() {
        List<Client> clients = clientService.getPendingClients();
        return Result.success(clients);
    }

    /**
     * 更新客户状态
     *
     * @param params 客户状态参数，包含clientId和status
     * @return 操作结果
     */
    @PostMapping("/update-status")
    public Result<Void> updateClientStatus(@RequestBody Map<String, Object> params) {
        if (params.get("clientId") == null || params.get("status") == null) {
            return Result.validateFailed("您无法修改其他员工的客户；客户ID和状态不能为空");
        }

        Integer clientId = Integer.parseInt(params.get("clientId").toString());
        String status = params.get("status").toString();
        String rejectRemark = (String) params.get("rejectRemark"); // 可以为null

        int result = clientService.updateClientStatus(clientId, status, rejectRemark);
        if (result > 0) {
            return Result.success("更新客户状态成功", null);
        } else {
            return Result.error("更新客户状态失败，客户可能不存在");
        }
    }

    /**
     * 批量更新客户状态
     *
     * @param params 客户状态参数，包含clientIds列表和status
     * @return 操作结果
     */
    @PostMapping("/batch-update-status")
    public Result<Void> batchUpdateClientStatus(@RequestBody Map<String, Object> params) {
        if (params.get("clientIds") == null || params.get("status") == null) {
            return Result.validateFailed("客户ID列表和状态不能为空");
        }

        @SuppressWarnings("unchecked")
        List<Integer> clientIds = (List<Integer>) params.get("clientIds");
        String status = params.get("status").toString();
        String rejectRemark = (String) params.get("rejectRemark"); // 可以为null

        if (clientIds.isEmpty()) {
            return Result.validateFailed("客户ID列表不能为空");
        }

        int result = clientService.batchUpdateClientStatus(clientIds, status, rejectRemark);
        if (result > 0) {
            return Result.success("批量更新客户状态成功", null);
        } else {
            return Result.error("批量更新客户状态失败");
        }
    }

    /**
     * 获取待审批的客户数量
     *
     * @return 待审批客户数量
     */
    @GetMapping("/pending-count")
    public Result<Integer> getPendingClientCount() {
        int count = clientService.getPendingClientCount();
        return Result.success(count);
    }

    /**
     * 根据部门ID列表分页查询客户
     * @param requestBody 请求体，包含分页和过滤参数
     * @return 分页结果
     */
    @PostMapping("/department-clients")
    public Result<PageResult<Client>> getClientsByDepartments(@RequestBody Map<String, Object> requestBody) {
        // 从 requestBody 中提取参数
        Integer page = (Integer) requestBody.getOrDefault("page", 1);
        Integer size = (Integer) requestBody.getOrDefault("size", 10);
        @SuppressWarnings("unchecked")
        List<Integer> departmentIdList = (List<Integer>) requestBody.get("departmentIds");
        String clientName = (String) requestBody.get("clientName");
        String employeeName = (String) requestBody.get("employeeName");
        String category = (String) requestBody.get("category");
        String status = (String) requestBody.get("status");

        // 移除 departmentIds.split(",") 逻辑，直接校验 departmentIdList
        if (departmentIdList == null || departmentIdList.isEmpty()) {
            return Result.validateFailed("部门ID列表不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("start", (page - 1) * size);
        params.put("size", size);
        params.put("departmentIds", departmentIdList);

        if (clientName != null && !clientName.trim().isEmpty()) {
            params.put("clientName", clientName.trim());
        }
        if (employeeName != null && !employeeName.trim().isEmpty()) {
            params.put("employeeName", employeeName.trim());
        }

        if (category != null && !category.trim().isEmpty()) {
            params.put("category", category.trim());
        }

        if (status != null && !status.trim().isEmpty()) {
            params.put("status", status.trim());
        }

        List<Client> clients = clientService.getClientsByDepartments(params);
        int total = clientService.countClientsByDepartments(params);

        PageResult<Client> pageResult = new PageResult<>(page, size, total, clients);
        return Result.success(pageResult);
    }
} 