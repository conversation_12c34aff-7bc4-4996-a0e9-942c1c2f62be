package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.EmployeeNotification;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface EmployeeNotificationMapper {

    void insertNotification(EmployeeNotification notification);

    int updateNotification(EmployeeNotification notification);

    EmployeeNotification findById(@Param("notificationId") Long notificationId);

    /**
     * 查找员工所有需要在铃铛中显示的通知 (ACTIVE状态, 或 next_prompt_date 已到期但未处理)
     * 按照生成时间降序排列。
     * @param employeeId 员工ID
     * @return 通知列表
     */
    List<EmployeeNotification> findNotificationsForBellByEmployeeId(@Param("employeeId") Integer employeeId);


    int dismissNotificationById(@Param("notificationId") Long notificationId,
                                @Param("dismissedAt") LocalDateTime dismissedAt,
                                @Param("nextPromptDate") LocalDate nextPromptDate,
                                @Param("status") String status);

    int markAsReadInBellById(@Param("notificationId") Long notificationId, @Param("isReadInBell") boolean isReadInBell);

    int dismissMultipleNotificationsByIds(@Param("ids") List<Long> ids,
                                        @Param("dismissedAt") LocalDateTime dismissedAt,
                                        @Param("nextPromptDate") LocalDate nextPromptDate,
                                        @Param("status") String status);
    /**
     * 查找特定员工针对特定规则的，当前是ACTIVE状态且next_prompt_date未到，
     * 或者在近期的dismissed_at且next_prompt_date还没到的通知。
     * 目的是避免对同一未解决问题重复生成完全相同的通知，除非到了下次提醒时间。
     * @param employeeId 员工ID
     * @param ruleId 规则ID
     * @param today 当前日期，用于比较 next_prompt_date
     * @return 如果存在这样的通知，则返回该通知，否则返回null
     */
    EmployeeNotification findExistingNotificationByRule( // Renamed from findByEmployeeIdAndRuleIdAndActiveOrRecentDismiss
        @Param("employeeId") Integer employeeId,
        @Param("ruleId") Integer ruleId,
        @Param("today") LocalDate today
    );

    /**
     * (此方法可能在服务层用于更复杂的查询，或者暂时不用，先保留)
     * 查找员工所有处于 'ACTIVE' 状态且 next_prompt_date 为空或小于等于今天的通知。
     * @param employeeId 员工ID
     * @param today 当前日期
     * @return 通知列表
     */
    List<EmployeeNotification> findActiveAndDueByEmployeeId(@Param("employeeId") Integer employeeId, @Param("today") LocalDate today);

} 