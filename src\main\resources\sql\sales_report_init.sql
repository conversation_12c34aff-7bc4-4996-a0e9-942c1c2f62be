-- {{CHENGQI: 销售日报功能数据库初始化脚本}}
-- {{CHENGQI: 任务ID: P1-AR-001}}
-- {{CHENGQI: 负责人: AR}}
-- {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
-- {{CHENGQI: 描述: 创建销售日报相关数据表和初始数据}}

-- 使用现有数据库
USE `company_management_system`;

-- 创建销售日报表
CREATE TABLE IF NOT EXISTS `sales_daily_report`
(
    `id`                         BIGINT                      NOT NULL AUTO_INCREMENT COMMENT '日报ID',
    `employee_id`                INT                         NOT NULL COMMENT '员工ID',
    `report_date`                DATE                        NOT NULL COMMENT '日报日期',

    -- 统计字段（系统自动计算）
    `yearly_new_clients`         INT                         NOT NULL DEFAULT 0 COMMENT '年度新客户总数',
    `monthly_new_clients`        INT                         NOT NULL DEFAULT 0 COMMENT '当月新客户总数',
    `days_since_last_new_client` INT                         NOT NULL DEFAULT 0 COMMENT '距离上次出新客户天数',

    -- 客户选择字段（JSON格式存储客户ID数组）
    `inquiry_clients`            JSON COMMENT '询价客户ID列表',
    `shipping_clients`           JSON COMMENT '出货客户ID列表',
    `key_development_clients`    JSON COMMENT '重点开发客户ID列表',

    -- 评估字段
    `responsibility_level`       ENUM ('优秀', '中等', '差') NOT NULL COMMENT '责任心评级',

    -- 工作检查清单（JSON格式存储）
    `end_of_day_checklist`       JSON COMMENT '下班准备工作检查清单',

    -- 文本输入字段
    `daily_results`              TEXT COMMENT '今日效果',
    `meeting_report`             TEXT COMMENT '会议报告',
    `work_diary`                 TEXT COMMENT '工作日记',

    -- 系统字段
    `create_time`                DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `report_date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_report_date` (`report_date`),
    INDEX `idx_employee_date` (`employee_id`, `report_date`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售日报表';
