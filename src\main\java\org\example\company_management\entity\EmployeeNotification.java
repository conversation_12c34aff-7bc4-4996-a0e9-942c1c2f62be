package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeNotification {
    private Long notificationId;
    private Integer employeeId;
    private Integer ruleId; // Nullable
    private String notificationType; // From rule condition_type or a custom type
    private String titleCn;
    private String messageCn;
    private LocalDateTime generatedAt;
    private LocalDateTime lastShownAt; // Nullable
    private LocalDateTime dismissedAt; // Nullable
    private Integer showAgainAfterDays; // Nullable, from rule or custom
    private LocalDate nextPromptDate; // Nullable
    private Boolean isReadInBell;
    private String status; // e.g., "ACTIVE", "DISMISSED", "ARCHIVED"

    // Transient field to hold rule details if needed, not persisted directly in this table
    // private NotificationRule rule;
} 