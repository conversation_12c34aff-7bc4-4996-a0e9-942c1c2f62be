<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.example.company_management.mapper.ClientMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.Client">
        <id column="client_id" property="clientId"/>
        <result column="name" property="name"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="remark" property="remark"/>
        <result column="reject_remark" property="rejectRemark"/>
        <result column="employee_id" property="employeeId"/>
        <result column="category" property="category"/>
        <result column="status" property="status"/>
        <result column="client_status" property="clientStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 查询结果列 -->
    <sql id="Base_Column_List">
        client_id
        , name, email, phone, employee_id, category, status, client_status, create_time, update_time
    </sql>

    <!-- 查询客户，关联员工表获取员工名称 -->
    <resultMap id="ClientWithEmployeeMap" type="org.example.company_management.entity.Client" extends="BaseResultMap">
        <result column="employee_name" property="employeeName"/>
        <result column="department_name" property="departmentName"/>
    </resultMap>

    <!-- 根据客户ID查询 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
               e.name AS employee_name
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
        WHERE c.client_id = #{clientId}
    </select>

    <!-- 查询所有客户 -->
    <select id="selectAll" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
               e.name AS employee_name
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
        WHERE c.status != '已删除'
    </select>

    <!-- 分页查询客户 -->
    <select id="selectByPage" parameterType="java.util.Map" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
        e.name AS employee_name,
        d.department_name AS department_name
        FROM client c
        LEFT JOIN employee e ON c.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        <where>
            <if test="name != null and name != ''">
                AND c.name LIKE #{name}
            </if>
            <if test="email != null and email != ''">
                AND c.email LIKE #{email}
            </if>
            <if test="phone != null and phone != ''">
                AND c.phone LIKE #{phone}
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE #{employeeName}
            </if>
            <if test="category != null and category != ''">
                AND c.category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            AND c.status != '已删除'
        </where>
        ORDER BY c.create_time DESC
        LIMIT #{start}, #{limit}
    </select>

    <!-- 获取客户总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(c.client_id)
        FROM client c
        LEFT JOIN employee e ON c.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        <where>
            <if test="name != null and name != ''">
                AND c.name LIKE #{name}
            </if>
            <if test="email != null and email != ''">
                AND c.email LIKE #{email}
            </if>
            <if test="phone != null and phone != ''">
                AND c.phone LIKE #{phone}
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE #{employeeName}
            </if>
            <if test="category != null and category != ''">
                AND c.category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            AND c.status != '已删除'
        </where>
    </select>

    <!-- 新增客户 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Client" useGeneratedKeys="true"
            keyProperty="clientId">
        INSERT INTO client
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="email != null">email,</if>
            <if test="phone != null">phone,</if>
            <if test="remark != null">remark,</if>
            <if test="rejectRemark != null">reject_remark,</if>
            <if test="nationality != null">nationality,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="category != null">category,</if>
            <if test="status != null">status,</if>
            <if test="clientStatus != null">client_status,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="email != null">#{email},</if>
            <if test="phone != null">#{phone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="rejectRemark != null">#{rejectRemark},</if>
            <if test="nationality != null">#{nationality},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="category != null">#{category},</if>
            <if test="status != null">#{status},</if>
            <if test="clientStatus != null">#{clientStatus},</if>
            NOW(),
            NOW()
        </trim>
    </insert>

    <!-- 修改客户 -->
    <update id="update" parameterType="org.example.company_management.entity.Client">
        UPDATE client
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="rejectRemark != null">reject_remark = #{rejectRemark},</if>
            <if test="nationality != null">nationality = #{nationality},</if>
            operation_time = #{operationTime},
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="category != null">category = #{category},</if>
            <if test="status != null">status = #{status},</if>
            <if test="clientStatus != null">client_status = #{clientStatus},</if>
            update_time = NOW()
        </set>
        WHERE client_id = #{clientId}
    </update>

    <!-- 软删除客户 软删除客户，将客户状态设定为已删除-->
    <delete id="deleteById" parameterType="java.lang.Integer">
       UPDATE client
        SET status = '已删除',
            update_time = NOW()
        WHERE client_id = #{clientId}
    </delete>

    <!-- 根据员工ID查询客户 -->
    <select id="selectByEmployee" parameterType="java.lang.Integer" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
               e.name AS employee_name
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
        WHERE c.employee_id = #{employeeId}
          AND c.status != '已删除'
    </select>

    <!-- 根据名称查询客户 -->
    <select id="selectByName" parameterType="java.lang.String" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
               e.name AS employee_name
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
        WHERE c.name LIKE CONCAT('%', #{name}, '%')
          AND c.status != '已删除'
    </select>

    <!-- 根据邮箱查询客户 -->
    <select id="selectByEmail" parameterType="java.lang.String" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
               e.name AS employee_name
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
        WHERE c.email = #{email}
          AND c.status != '已删除'
    </select>

    <!-- 根据电话查询客户 -->
    <select id="selectByPhone" parameterType="java.lang.String" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
               e.name AS employee_name
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
        WHERE c.phone = #{phone}
          AND c.status != '已删除'
    </select>

    <!-- 分页查询当前登录用户负责的客户 -->
    <select id="selectMyClientsByPage" parameterType="java.util.Map" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
        e.name AS employee_name
        FROM client c
        LEFT JOIN employee e ON c.employee_id = e.employee_id
        <where>
            c.employee_id = #{employeeId}
            <if test="name != null and name != ''">
                AND c.name LIKE #{name}
            </if>
            <if test="category != null and category != ''">
                AND c.category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            <if test="clientStatus != null and clientStatus != ''">
                AND c.client_status = #{clientStatus}
            </if>
            AND c.status != '已删除'
        </where>
        ORDER BY c.create_time DESC
        LIMIT #{start}, #{limit}
    </select>

    <!-- 获取当前登录用户负责的客户总数 -->
    <select id="countMyClientsTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM client c
        <where>
            c.employee_id = #{employeeId}
            <if test="name != null and name != ''">
                AND c.name LIKE #{name}
            </if>
            <if test="category != null and category != ''">
                AND c.category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            <if test="clientStatus != null and clientStatus != ''">
                AND c.client_status = #{clientStatus}
            </if>
            AND c.status != '已删除'
        </where>
    </select>

    <!--根据客户名称查询其他客户 根据创建时间最多返回5条结果-->
    <select id="selectOtherClientsByName" parameterType="java.lang.String" resultMap="ClientWithEmployeeMap">
        SELECT c.name,
               e.name AS employee_name,
               c.category,
               c.status,
               c.create_time,
               c.update_time
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
        WHERE c.name LIKE CONCAT('%', #{name}, '%')
          AND c.status != '已删除'
        ORDER BY c.create_time DESC
        LIMIT 5
    </select>

    <!-- 获取待审批的客户列表 -->
    <select id="selectPendingClients" resultMap="ClientWithEmployeeMap">
        SELECT c.*,
               e.name AS employee_name,
               d.department_name AS department_name 
        FROM client c
                 LEFT JOIN employee e ON c.employee_id = e.employee_id
                 LEFT JOIN department d ON e.department_id = d.department_id
        WHERE c.status = '审核中'
        ORDER BY c.create_time DESC
    </select>

    <!-- 更新客户状态 -->
    <update id="updateClientStatus" parameterType="java.util.Map">
        UPDATE client
        <set>
            status = #{status},
            update_time = NOW(),
            <if test="status == '审核通过'">
                operation_time = NOW(),
            </if>
            <if test="status == '已拒绝'">
                reject_remark = #{rejectRemark},
            </if>
            <if test="status != '已拒绝'">
                reject_remark = NULL,
            </if>
        </set>
        WHERE client_id = #{clientId}
    </update>

    <!-- 批量更新客户状态 -->
    <update id="batchUpdateClientStatus" parameterType="java.util.Map">
        UPDATE client
        <set>
            status = #{status},
            update_time = NOW(),
            <if test="status == '已拒绝' and rejectRemark != null">
                reject_remark = #{rejectRemark},
            </if>
            <if test="status != '已拒绝'">
                reject_remark = NULL,
            </if>
        </set>
        WHERE client_id IN
        <foreach collection="clientIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取待审批的客户数量 -->
    <select id="countPendingClients" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM client
        WHERE status = '审核中'
    </select>

    <!-- 根据部门ID列表查询客户（分页） -->
    <select id="selectClientsByDepartments" parameterType="java.util.Map" resultMap="ClientWithEmployeeMap">
        SELECT DISTINCT c.*,
            e.name AS employee_name,
            d.department_name AS department_name
        FROM client c
        JOIN employee e ON c.employee_id = e.employee_id
        JOIN department d ON e.department_id = d.department_id
        <where>
            d.department_id IN
            <foreach collection="departmentIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="clientName != null and clientName != ''">
                AND c.name LIKE CONCAT('%', #{clientName}, '%')
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="category != null and category != ''">
                AND c.category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            AND c.status != '已删除'
        </where>
        ORDER BY c.create_time DESC
        LIMIT #{start}, #{size}
    </select>

    <!-- 根据部门ID列表获取客户总数 -->
    <select id="countClientsByDepartments" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT c.client_id)
        FROM client c
        JOIN employee e ON c.employee_id = e.employee_id
        JOIN department d ON e.department_id = d.department_id
        <where>
            d.department_id IN
            <foreach collection="departmentIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="clientName != null and clientName != ''">
                AND c.name LIKE CONCAT('%', #{clientName}, '%')
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="category != null and category != ''">
                AND c.category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            AND c.status != '已删除'
        </where>
    </select>

   

    <select id="countByEmployeeIdAndStatusAndCreateTimeBetween" resultType="long">
        SELECT COUNT(client_id)
        FROM client
        WHERE employee_id = #{employeeId}
          AND status = #{status}
          AND create_time >= STR_TO_DATE(#{startTimeInclusive}, '%Y-%m-%dT%H:%i:%S')
          AND create_time &lt; STR_TO_DATE(#{endTimeExclusive}, '%Y-%m-%dT%H:%i:%S')
    </select>

    <!-- {{CHENGQI: 为销售日报功能添加的客户统计查询}} -->
    <!-- {{CHENGQI: 任务ID: P1-LD-004}} -->
    <!-- {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}} -->
    <!-- {{CHENGQI: 修改时间: 2025-06-04 15:54:04 +08:00}} -->
    <!-- {{CHENGQI: 修改内容: 优化查询条件，使用排除条件而非单一状态匹配，排除"报价中"、"已拒绝"、"已取消"、"已删除"状态}} -->

    <!-- 统计员工年度新客户数量 -->
    <select id="countNewClientsByEmployeeAndYear" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM client
        WHERE employee_id = #{employeeId}
          AND status = '审核通过'
          AND client_status = '已合作'
          AND YEAR(create_time) = #{year}
    </select>

    <!-- 统计员工当月新客户数量 -->
    <select id="countNewClientsByEmployeeAndMonth" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM client
        WHERE employee_id = #{employeeId}
          AND status = '审核通过'
          AND client_status = '已合作'
          AND YEAR(create_time) = #{year}
          AND MONTH(create_time) = #{month}
    </select>

    <!-- 计算距离上次新客户的天数 -->
    <select id="calculateDaysSinceLastNewClient" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT DATEDIFF(CURDATE(), MAX(create_time))
        FROM client
        WHERE employee_id = #{employeeId}
          AND status = '审核通过'
          AND client_status = '已合作'
    </select>

</mapper>