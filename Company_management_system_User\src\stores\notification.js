import { defineStore } from 'pinia';
import { getPopupNotifications, getBellNotifications, dismissNotification, markAsReadInBell, dismissMultipleNotifications } from '@/api/notification';
import { ElMessage } from 'element-plus';

export const useNotificationStore = defineStore('notification', {
    state: () => ({
        popupNotifications: [], // 当前需要弹窗的通知
        bellNotifications: [],  // 铃铛中显示的通知列表
        hasUnreadBellMessage: false, // 铃铛是否有未读消息（用于显示红点）
        isLoadingPopup: false,
        isLoadingBell: false,
        useMockData: false, // 修改为 false
        shouldOpenCenterOnLoginIfUnread: false, // 新状态：登录时若有未读是否应打开通知中心
    }),
    actions: {
        async fetchPopupNotifications() {
            if (this.isLoadingPopup) return;
            this.isLoadingPopup = true;

            try {
                const response = await getPopupNotifications();
                if (response.data && response.data.code === 200) {
                    // 根据需求，登录时不主动用这个弹单个，而是检查铃铛通知
                    // this.popupNotifications = response.data.data || []; 
                } else {
                    // ElMessage.error(response.data.message || '获取弹窗通知失败[API]');
                }
            } catch (error) {
                console.error('Error fetching popup notifications [API]:', error);
                // ElMessage.error('获取弹窗通知时发生网络错误[API]');
            } finally {
                this.isLoadingPopup = false;
            }
        },

        async fetchBellNotifications(isInitialLoginLoad = false) {
            if (this.isLoadingBell) return;
            this.isLoadingBell = true;
            this.shouldOpenCenterOnLoginIfUnread = false; // 重置

            try {
                const response = await getBellNotifications();
                if (response.code === 200) {
                    this.bellNotifications = response.data || [];
                    this.hasUnreadBellMessage = this.bellNotifications.some(n => !n.isReadInBell && n.status !== 'DISMISSED');
                    if (isInitialLoginLoad && this.hasUnreadBellMessage) {
                        console.log("API: Setting shouldOpenCenterOnLoginIfUnread to true due to unread messages.");
                        this.shouldOpenCenterOnLoginIfUnread = true;
                    }
                } else {
                    this.bellNotifications = [];
                    this.hasUnreadBellMessage = false;
                    ElMessage.error(response.message || '获取铃铛通知失败[API]');
                }
            } catch (error) {
                this.bellNotifications = [];
                this.hasUnreadBellMessage = false;
                console.error('Error fetching bell notifications [API]:', error);
                ElMessage.error('获取铃铛通知时发生网络错误[API]');
            } finally {
                this.isLoadingBell = false;
            }
        },

        async handleDismissNotification(notificationId) {
            try {
                const response = await dismissNotification(notificationId);
                if (response && response.code === 200) {
                    ElMessage.success(response.data || response.message || '通知已处理');
                    this.fetchBellNotifications();
                } else {
                    ElMessage.error(response?.message || '处理通知失败[API]');
                }
            } catch (error) {
                console.error('Error dismissing notification [API]:', error);
                ElMessage.error('处理通知时发生网络错误[API]');
            }
        },

        async handleMarkAsReadInBell(notificationId) {
            try {
                const response = await markAsReadInBell(notificationId);
                if (response && response.code === 200) {
                    const index = this.bellNotifications.findIndex(n => n.notificationId === notificationId);
                    if (index !== -1) {
                        this.bellNotifications[index].isReadInBell = true;
                    }
                    this.hasUnreadBellMessage = this.bellNotifications.some(n => !n.isReadInBell && n.status !== 'DISMISSED');
                    // ElMessage.success(response.data || response.message || '通知已在铃铛中标记为已读');
                } else {
                    ElMessage.error(response?.message || '标记已读失败[API]');
                }
            } catch (error) {
                console.error('Error marking notification as read in bell [API]:', error);
                ElMessage.error('标记已读时发生网络错误[API]');
            }
        },

        // 清空通知，例如用户登出时
        clearNotifications() {
            this.popupNotifications = [];
            this.bellNotifications = [];
            this.hasUnreadBellMessage = false;
        },
        setOpenedCenterOnLogin() { // 新增action，用于在通知中心打开后重置标志
            this.shouldOpenCenterOnLoginIfUnread = false;
        },
        async handleDismissMultipleNotifications(notificationIds) {
            try {
                const response = await dismissMultipleNotifications(notificationIds);
                if (response && response.code === 200) {
                    ElMessage.success(response.data || response.message || '批量通知已处理');
                    this.fetchBellNotifications();
                } else {
                    ElMessage.error(response?.message || '批量处理通知失败[API]');
                }
            } catch (error) {
                console.error('Error dismissing multiple notifications [API]:', error);
                ElMessage.error('批量处理通知时发生网络错误[API]');
            }
        }
    },
    getters: {
        // 可以添加一些getter，例如未读铃铛消息数量等
        unreadBellNotificationCount: (state) => {
            return state.bellNotifications.filter(n => !n.isReadInBell && n.status !== 'DISMISSED').length;
        }
    }
}); 