package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Client {
    /**
     * 客户ID
     */
    private Integer clientId;
    
    /**
     * 客户名称
     */
    private String name;
    
    /**
     * 客户邮箱
     */
    private String email;
    
    /**
     * 客户电话
     */
    private String phone;
    
    /**
     * 负责员工ID
     */
    private Integer employeeId;
    
    /**
     * 客户分类（海运、散货、空运、快递等）
     */
    private String category;
    
    /**
     * 合作状态（审核中、已拒绝、报价中、已合作）
     */
    private String status;
    
    /**
     * 操作时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operationTime;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 负责员工姓名（非数据库字段）
     */
    private String employeeName;

    /**
     * 负责部门名称（非数据库字段）
     */
    private String departmentName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 员工信息（非数据库字段）
     */
    private Employee employee;

    /**
     * 联系人
     */ 
    private String contactPerson;

    /**
     * 备注
     */
    private String remark;

    /**
     * 拒绝备注
     */
    private String rejectRemark;
} 